# 开发指南

## 项目概述

本项目是从 WePY2 框架迁移到 Taro + Vue3 + TaroUI 技术栈的微信小程序项目。

## 技术栈

- **框架**: Taro 4.x
- **前端框架**: Vue3 (Composition API)
- **UI组件库**: TaroUI Vue3
- **样式预处理**: Less
- **类型支持**: TypeScript
- **网络请求**: Taro.request

## 开发环境要求

- Node.js >= 16.0.0
- npm >= 8.0.0
- 微信开发者工具

## 快速开始

### 1. 安装依赖
```bash
npm install
```

### 2. 开发模式
```bash
# 微信小程序
npm run dev:weapp

# H5
npm run dev:h5

# 支付宝小程序
npm run dev:alipay
```

### 3. 生产构建
```bash
# 微信小程序
npm run build:weapp

# H5
npm run build:h5
```

### 4. 验证迁移
```bash
node scripts/test-migration.js
```

## 项目结构

```
taro-vue-project/
├── src/
│   ├── pages/              # 页面文件
│   │   └── index/          # 首页
│   │       ├── index.vue   # 页面组件
│   │       ├── index.config.ts  # 页面配置
│   │       └── index.less  # 页面样式
│   ├── services/           # API服务
│   │   ├── api.ts         # API接口定义
│   │   └── request.ts     # 请求封装
│   ├── common/            # 通用文件
│   │   └── common.less    # 通用样式
│   ├── app.ts             # 应用入口
│   ├── app.config.ts      # 应用配置
│   └── app.less           # 全局样式
├── scripts/               # 脚本文件
│   └── test-migration.js  # 迁移验证脚本
├── MIGRATION_SUMMARY.md   # 迁移总结
└── DEVELOPMENT_GUIDE.md   # 开发指南
```

## 开发规范

### 1. 代码规范

#### 命名规范
- 文件名：kebab-case (如：user-profile.vue)
- 组件名：PascalCase (如：UserProfile)
- 变量/函数：camelCase (如：getUserInfo)
- 常量：UPPER_SNAKE_CASE (如：API_BASE_URL)

#### 注释规范
- 所有注释使用中文
- 函数必须有注释说明
- 复杂逻辑需要详细注释

### 2. Vue3 开发规范

#### 使用 Composition API
```vue
<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'

// 响应式数据
const count = ref(0)
const userInfo = reactive({
  name: '',
  age: 0
})

// 计算属性
const doubleCount = computed(() => count.value * 2)

// 生命周期
onMounted(() => {
  console.log('组件已挂载')
})

// 方法
const handleClick = () => {
  count.value++
}
</script>
```

#### 组件通信
```vue
<!-- 父组件 -->
<template>
  <ChildComponent 
    :data="parentData" 
    @update="handleUpdate" 
  />
</template>

<!-- 子组件 -->
<script setup lang="ts">
interface Props {
  data: any
}

const props = defineProps<Props>()
const emit = defineEmits<{
  update: [value: any]
}>()

const handleChange = (value: any) => {
  emit('update', value)
}
</script>
```

### 3. 样式规范

#### 使用 rpx 单位
```less
.container {
  width: 750rpx;
  padding: 20rpx;
  font-size: 28rpx;
}
```

#### 使用通用样式混合
```less
@import '../../common/common.less';

.my-component {
  .flex(center, space-between);
  .card();
  
  .title {
    .title-medium();
    .ellipsis();
  }
}
```

### 4. API 调用规范

#### 定义 API 接口
```typescript
// src/services/api.ts
export function getUserInfo(data: { userId: string }) {
  return post('/getUserInfo', { data })
}
```

#### 在组件中使用
```vue
<script setup lang="ts">
import { getUserInfo } from '@/services/api'

const loadUserInfo = async () => {
  try {
    const response = await getUserInfo({ userId: '123' })
    if (response.data && response.data.d) {
      // 处理数据
    }
  } catch (error) {
    console.error('获取用户信息失败:', error)
    Taro.showToast({
      title: '获取数据失败',
      icon: 'none'
    })
  }
}
</script>
```

## 常用功能

### 1. 页面跳转
```typescript
// 普通跳转
Taro.navigateTo({
  url: '/pages/detail/index?id=123'
})

// 重定向
Taro.redirectTo({
  url: '/pages/login/index'
})

// 返回上一页
Taro.navigateBack()
```

### 2. 数据存储
```typescript
// 存储数据
Taro.setStorageSync('userInfo', userInfo)

// 获取数据
const userInfo = Taro.getStorageSync('userInfo')

// 删除数据
Taro.removeStorageSync('userInfo')
```

### 3. 显示提示
```typescript
// 成功提示
Taro.showToast({
  title: '操作成功',
  icon: 'success'
})

// 错误提示
Taro.showToast({
  title: '操作失败',
  icon: 'none'
})

// 加载提示
Taro.showLoading({
  title: '加载中...'
})

Taro.hideLoading()
```

## 调试技巧

### 1. 开发者工具调试
- 使用微信开发者工具的调试功能
- 查看 Console 输出
- 使用 Network 面板查看网络请求

### 2. 真机调试
```bash
# 开启真机调试
npm run dev:weapp
```
然后在微信开发者工具中选择"真机调试"

### 3. 性能分析
- 使用微信开发者工具的性能面板
- 关注页面加载时间和内存使用

## 常见问题

### 1. 样式不生效
- 检查 rpx 单位是否正确
- 确认样式文件是否正确导入
- 检查样式选择器优先级

### 2. API 请求失败
- 检查网络连接
- 确认 API 地址是否正确
- 查看请求参数格式

### 3. 组件不显示
- 检查组件导入路径
- 确认组件注册是否正确
- 查看控制台错误信息

## 部署指南

### 1. 构建生产版本
```bash
npm run build:weapp
```

### 2. 上传代码
1. 在微信开发者工具中打开 `dist` 目录
2. 点击"上传"按钮
3. 填写版本号和项目备注
4. 提交审核

### 3. 发布小程序
1. 登录微信公众平台
2. 进入小程序管理后台
3. 在"开发管理"中提交审核
4. 审核通过后发布

## 更多资源

- [Taro 官方文档](https://taro-docs.jd.com/)
- [Vue3 官方文档](https://cn.vuejs.org/)
- [TaroUI 文档](https://taro-ui-vue3.vercel.app/)
- [微信小程序开发文档](https://developers.weixin.qq.com/miniprogram/dev/framework/)

## 联系方式

如有问题，请联系开发团队或查看项目文档。
