#!/usr/bin/env node

/**
 * WePY2 到 Taro 迁移验证脚本
 * 用于验证迁移后的项目是否正常工作
 */

const fs = require('fs');
const path = require('path');

console.log('🚀 开始验证 WePY2 到 Taro 迁移结果...\n');

// 验证项目
const checks = [
  {
    name: '页面文件存在性检查',
    check: () => {
      const files = [
        'src/pages/index/index.vue',
        'src/pages/index/index.config.ts',
        'src/pages/index/index.less'
      ];
      
      const missing = files.filter(file => !fs.existsSync(path.join(process.cwd(), file)));
      
      if (missing.length > 0) {
        throw new Error(`缺失文件: ${missing.join(', ')}`);
      }
      
      return '✅ 所有页面文件都存在';
    }
  },
  
  {
    name: 'API服务文件检查',
    check: () => {
      const apiFile = 'src/services/api.ts';
      
      if (!fs.existsSync(path.join(process.cwd(), apiFile))) {
        throw new Error(`API服务文件不存在: ${apiFile}`);
      }
      
      const content = fs.readFileSync(path.join(process.cwd(), apiFile), 'utf8');
      const requiredApis = [
        'homeBanner',
        'menuList',
        'NewsListing',
        'LimitedTimeProduct',
        'GroupPurchaseProduct',
        'HomePageCountryBrand',
        'ServiceHomePageMainCategory'
      ];
      
      const missingApis = requiredApis.filter(api => !content.includes(api));
      
      if (missingApis.length > 0) {
        throw new Error(`缺失API接口: ${missingApis.join(', ')}`);
      }
      
      return '✅ API服务文件完整';
    }
  },
  
  {
    name: 'Vue3组件语法检查',
    check: () => {
      const vueFile = path.join(process.cwd(), 'src/pages/index/index.vue');
      const content = fs.readFileSync(vueFile, 'utf8');
      
      // 检查Vue3特性
      const vue3Features = [
        '<script setup lang="ts">',
        'import { ref, onMounted, computed }',
        'defineExpose'
      ];
      
      const missingFeatures = vue3Features.filter(feature => !content.includes(feature));
      
      if (missingFeatures.length > 0) {
        throw new Error(`缺失Vue3特性: ${missingFeatures.join(', ')}`);
      }
      
      return '✅ Vue3组件语法正确';
    }
  },
  
  {
    name: 'TaroUI组件使用检查',
    check: () => {
      const vueFile = path.join(process.cwd(), 'src/pages/index/index.vue');
      const content = fs.readFileSync(vueFile, 'utf8');
      
      // 检查TaroUI组件
      const taroComponents = [
        'AtButton',
        'AtModal',
        'AtInput'
      ];
      
      const missingComponents = taroComponents.filter(comp => !content.includes(comp));
      
      if (missingComponents.length > 0) {
        throw new Error(`缺失TaroUI组件: ${missingComponents.join(', ')}`);
      }
      
      return '✅ TaroUI组件使用正确';
    }
  },
  
  {
    name: '样式文件检查',
    check: () => {
      const lessFile = path.join(process.cwd(), 'src/pages/index/index.less');
      const content = fs.readFileSync(lessFile, 'utf8');
      
      // 检查关键样式类
      const requiredStyles = [
        '.container',
        '.custom-header',
        '.main',
        '.swiper',
        '.tabs',
        '.box'
      ];
      
      const missingStyles = requiredStyles.filter(style => !content.includes(style));
      
      if (missingStyles.length > 0) {
        throw new Error(`缺失样式类: ${missingStyles.join(', ')}`);
      }
      
      return '✅ 样式文件完整';
    }
  },
  
  {
    name: '通用样式文件检查',
    check: () => {
      const commonFile = path.join(process.cwd(), 'src/common/common.less');
      
      if (!fs.existsSync(commonFile)) {
        throw new Error('通用样式文件不存在');
      }
      
      const content = fs.readFileSync(commonFile, 'utf8');
      
      // 检查通用样式特性
      const features = [
        '@primary-color',
        '.flex(',
        '.ellipsis(',
        '.btn-base('
      ];
      
      const missingFeatures = features.filter(feature => !content.includes(feature));
      
      if (missingFeatures.length > 0) {
        throw new Error(`缺失通用样式特性: ${missingFeatures.join(', ')}`);
      }
      
      return '✅ 通用样式文件完整';
    }
  },
  
  {
    name: '编译产物检查',
    check: () => {
      const distFiles = [
        'dist/pages/index/index.wxml',
        'dist/pages/index/index.wxss',
        'dist/pages/index/index.js',
        'dist/pages/index/index.json'
      ];
      
      const missing = distFiles.filter(file => !fs.existsSync(path.join(process.cwd(), file)));
      
      if (missing.length > 0) {
        throw new Error(`缺失编译产物: ${missing.join(', ')}`);
      }
      
      return '✅ 编译产物完整';
    }
  }
];

// 执行检查
let passedChecks = 0;
let totalChecks = checks.length;

for (const check of checks) {
  try {
    console.log(`🔍 ${check.name}...`);
    const result = check.check();
    console.log(`   ${result}\n`);
    passedChecks++;
  } catch (error) {
    console.log(`   ❌ ${error.message}\n`);
  }
}

// 输出结果
console.log('📊 验证结果:');
console.log(`   通过: ${passedChecks}/${totalChecks}`);
console.log(`   成功率: ${Math.round((passedChecks / totalChecks) * 100)}%\n`);

if (passedChecks === totalChecks) {
  console.log('🎉 恭喜！WePY2 到 Taro 迁移验证全部通过！');
  console.log('📝 详细迁移信息请查看 MIGRATION_SUMMARY.md 文件');
  process.exit(0);
} else {
  console.log('⚠️  迁移验证未完全通过，请检查上述错误并修复');
  process.exit(1);
}
