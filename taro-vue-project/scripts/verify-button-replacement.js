#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

console.log('🔍 验证AtButton替换为原生button的结果...\n');

// 检查目标文件
const indexVueFile = 'src/pages/index/index.vue';
const indexVuePath = path.join(process.cwd(), indexVueFile);

if (!fs.existsSync(indexVuePath)) {
  console.error(`❌ 文件不存在: ${indexVueFile}`);
  process.exit(1);
}

const content = fs.readFileSync(indexVuePath, 'utf8');

// 检查是否还有AtButton
const atButtonMatches = content.match(/<AtButton[^>]*>/g) || [];
console.log('🔍 AtButton组件检查:');
if (atButtonMatches.length === 0) {
  console.log('   ✅ 没有发现AtButton组件，替换完成');
} else {
  console.log('   ❌ 仍然存在AtButton组件:');
  atButtonMatches.forEach(match => {
    console.log(`      ${match}`);
  });
}

// 检查原生button的使用
const buttonMatches = content.match(/<button[^>]*>/g) || [];
console.log('\n🔍 原生button组件检查:');
console.log(`   找到 ${buttonMatches.length} 个button组件`);

// 检查button的class使用
const btnClassMatches = content.match(/class="btn[123]-bg"/g) || [];
console.log('\n🔍 按钮样式类检查:');
console.log(`   找到 ${btnClassMatches.length} 个按钮样式类`);
btnClassMatches.forEach(match => {
  console.log(`   ✅ ${match}`);
});

// 检查事件绑定
const tapEventMatches = content.match(/@tap[^=]*="[^"]*"/g) || [];
console.log('\n🔍 事件绑定检查:');
console.log(`   找到 ${tapEventMatches.length} 个@tap事件`);

// 检查import语句
const importMatch = content.match(/import\s*\{[^}]*\}\s*from\s*['"]taro-ui-vue3['"]/);
console.log('\n🔍 Import语句检查:');
if (importMatch) {
  const importContent = importMatch[0];
  if (importContent.includes('AtButton')) {
    console.log('   ❌ 仍然导入了AtButton');
  } else {
    console.log('   ✅ 已移除AtButton的导入');
  }
  console.log(`   当前导入: ${importContent}`);
} else {
  console.log('   ❌ 未找到taro-ui-vue3的导入语句');
}

// 检查样式文件
const lessFile = 'src/pages/index/index.less';
const lessPath = path.join(process.cwd(), lessFile);

if (fs.existsSync(lessPath)) {
  const lessContent = fs.readFileSync(lessPath, 'utf8');
  
  console.log('\n🔍 样式文件检查:');
  
  // 检查按钮样式类
  const btnStyles = ['btn1-bg', 'btn2-bg', 'btn3-bg'];
  btnStyles.forEach(btnClass => {
    if (lessContent.includes(`.${btnClass}`)) {
      console.log(`   ✅ 找到样式类: .${btnClass}`);
    } else {
      console.log(`   ❌ 缺失样式类: .${btnClass}`);
    }
  });
  
  // 检查button通用样式
  if (lessContent.includes('button {')) {
    console.log('   ✅ 找到button通用样式');
  } else {
    console.log('   ❌ 缺失button通用样式');
  }
} else {
  console.log('\n❌ 样式文件不存在');
}

// 总结
console.log('\n📊 替换结果总结:');
const hasAtButton = atButtonMatches.length > 0;
const hasNativeButton = buttonMatches.length > 0;
const hasButtonStyles = btnClassMatches.length > 0;

console.log(`   AtButton移除: ${hasAtButton ? '未完成' : '已完成'}`);
console.log(`   原生button: ${hasNativeButton ? '已添加' : '未添加'}`);
console.log(`   按钮样式: ${hasButtonStyles ? '已应用' : '未应用'}`);

if (!hasAtButton && hasNativeButton && hasButtonStyles) {
  console.log('\n🎉 AtButton替换为原生button完成！所有检查都通过了。');
  process.exit(0);
} else {
  console.log('\n⚠️  AtButton替换存在问题，请检查上述错误。');
  process.exit(1);
}
