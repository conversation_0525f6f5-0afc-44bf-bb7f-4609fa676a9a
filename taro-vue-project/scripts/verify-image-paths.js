#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

console.log('🔍 验证图片路径修复结果...\n');

// 检查目标文件
const indexVueFile = 'src/pages/index/index.vue';
const indexVuePath = path.join(process.cwd(), indexVueFile);

if (!fs.existsSync(indexVuePath)) {
  console.error(`❌ 文件不存在: ${indexVueFile}`);
  process.exit(1);
}

const content = fs.readFileSync(indexVuePath, 'utf8');

// 检查静态资源路径定义
const staticResourcesRegex = /const\s+(\w+)\s*=\s*['"`]([^'"`]+)['"`]/g;
const staticResources = [];
let match;

while ((match = staticResourcesRegex.exec(content)) !== null) {
  if (match[2].includes('/static/')) {
    staticResources.push({
      name: match[1],
      path: match[2]
    });
  }
}

console.log('📁 静态资源路径定义:');
staticResources.forEach(resource => {
  const isCorrectPath = resource.path.startsWith('/static/');
  const status = isCorrectPath ? '✅' : '❌';
  console.log(`   ${status} ${resource.name}: ${resource.path}`);
});

// 检查是否还有硬编码的相对路径
const hardcodedRelativePaths = content.match(/src=["']\.\.\/.*?["']/g) || [];
console.log('\n🔍 硬编码相对路径检查:');
if (hardcodedRelativePaths.length === 0) {
  console.log('   ✅ 没有发现硬编码的相对路径');
} else {
  console.log('   ❌ 发现硬编码的相对路径:');
  hardcodedRelativePaths.forEach(path => {
    console.log(`      ${path}`);
  });
}

// 检查是否还有硬编码的绝对路径（除了CDN）
const hardcodedAbsolutePaths = content.match(/src=["']\/static\/[^"']*["']/g) || [];
console.log('\n🔍 硬编码绝对路径检查:');
if (hardcodedAbsolutePaths.length === 0) {
  console.log('   ✅ 没有发现硬编码的绝对路径');
} else {
  console.log('   ⚠️  发现硬编码的绝对路径:');
  hardcodedAbsolutePaths.forEach(path => {
    console.log(`      ${path}`);
  });
}

// 检查图片文件是否存在
console.log('\n📂 图片文件存在性检查:');
const staticDir = path.join(process.cwd(), 'src/static');
let allFilesExist = true;

staticResources.forEach(resource => {
  const filePath = path.join(staticDir, resource.path.replace('/static/', ''));
  const exists = fs.existsSync(filePath);
  const status = exists ? '✅' : '❌';
  console.log(`   ${status} ${resource.path}`);
  if (!exists) allFilesExist = false;
});

// 总结
console.log('\n📊 修复结果总结:');
const correctPaths = staticResources.filter(r => r.path.startsWith('/static/')).length;
const totalPaths = staticResources.length;
const hasHardcodedPaths = hardcodedRelativePaths.length > 0 || hardcodedAbsolutePaths.length > 0;

console.log(`   静态资源路径: ${correctPaths}/${totalPaths} 正确`);
console.log(`   硬编码路径: ${hasHardcodedPaths ? '存在问题' : '已清理'}`);
console.log(`   文件存在性: ${allFilesExist ? '全部存在' : '部分缺失'}`);

if (correctPaths === totalPaths && !hasHardcodedPaths && allFilesExist) {
  console.log('\n🎉 图片路径修复完成！所有检查都通过了。');
  process.exit(0);
} else {
  console.log('\n⚠️  图片路径修复存在问题，请检查上述错误。');
  process.exit(1);
}
