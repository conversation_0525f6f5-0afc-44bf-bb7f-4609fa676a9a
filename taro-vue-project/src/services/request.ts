// 网络请求服务 - 从WePY2的wefetch迁移到Taro
import Taro from '@tarojs/taro'

// 环境配置 - 从原WePY项目迁移
const env = process.env.NODE_ENV === 'production' ? 'release' : 'develop'
const baseApi = {
  // 开发版
  develop: 'https://api.annmun1.net/mpdebug.annmun1.net/System/Service.asmx',
  // 体验版
  trial: 'https://api.annmun1.net/mpdebug.annmun1.net/System/Service.asmx',
  // 正式版
  release: 'https://api.annmun1.net/System/Service.asmx'
}

const BASE_URL = baseApi[env]

// Toast提示函数 - 从原WePY项目迁移
const showToast = (msg: string) => {
  setTimeout(() => {
    Taro.showToast({
      icon: 'none',
      mask: true,
      duration: 2000,
      title: msg
    })
  })
}

// 获取当前页面URL和参数 - 从原WePY项目迁移
export const getCurrentPageUrlWithArgs = () => {
  const pages = Taro.getCurrentPages() // 获取加载的页面
  const currentPage = pages[pages.length - 1] // 获取当前页面的对象
  const url = currentPage.route // 当前页面url
  const options = currentPage.options // 如果要获取url中所带的参数可以查看options

  // 拼接url的参数
  let urlWithArgs = url + '?'
  for (const key in options) {
    const value = options[key]
    urlWithArgs += key + '=' + value + '&'
  }
  urlWithArgs = urlWithArgs.substring(0, urlWithArgs.length - 1)

  return { url: urlWithArgs, pages }
}

// 重新登录函数 - 从原WePY项目迁移
const reLogin = (ms = 0) => {
  setTimeout(() => {
    const { url } = getCurrentPageUrlWithArgs()
    Taro.removeStorageSync('Token')
    Taro.removeStorageSync('userInfo')
    Taro.setStorageSync('currentPage', url)
    Taro.redirectTo({ url: '/subpackages/pages/login2/index' })
  }, ms)
}

// 显示对话框 - 替换原来的Vant Dialog
const showDialog = (options: {
  title?: string
  message: string
  confirmButtonText?: string
  onConfirm?: () => void
}) => {
  return Taro.showModal({
    title: options.title || '温馨提示',
    content: options.message,
    confirmText: options.confirmButtonText || '确定',
    success: (res) => {
      if (res.confirm && options.onConfirm) {
        options.onConfirm()
      }
    }
  })
}

// 网络请求函数 - 将wefetch替换为Taro.request
const request = async (options: {
  url: string
  data?: any
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE'
}) => {
  const { url, data, method = 'POST' } = options

  // 请求前拦截 - 从原WePY项目迁移逻辑
  const noAuthUrl = ['']
  const requestUrl = url

  const Token = noAuthUrl.includes(url.replace(BASE_URL, ''))
    ? ''
    : Taro.getStorageSync('Token')
  const MemberCode = noAuthUrl.includes(url.replace(BASE_URL, ''))
    ? ''
    : Taro.getStorageSync('MemberCode')

  const defaultData = {
    Language: 'zh-CN',
    Token: Token || '',
    MemberCode: MemberCode || ''
  }

  const requestData = data ? Object.assign(data, defaultData) : defaultData
  
  try {
    const response = await Taro.request({
      url: BASE_URL + url,
      data: requestData,
      method,
      header: {
        'content-type': 'application/json'
      }
    })
    
    // 响应拦截 - 从原WePY项目迁移逻辑
    const { statusCode, data: responseData } = response
    
    if (statusCode !== 200 || responseData.d === false) {
      if (responseData.Message && responseData.Message.includes('Token')) {
        reLogin()
        return Promise.reject(response)
      }
      
      console.log('failRequestUrl:', requestUrl)
      console.log('failRequestData:', requestData)
      console.log('failResponse:', responseData)
      
      if (responseData.Message) {
        showDialog({
          title: '温馨提示',
          message: responseData.Message
        })
      } else {
        showToast('数据请求失败')
      }
      return Promise.reject(response)
    } else if (responseData.d) {
      try {
        const res = typeof responseData.d === 'string'
          ? JSON.parse(responseData.d.replace(/\n/gi, '\\n').replace(/\r/gi, '\\n'))
          : responseData.d
        
        if (
          Array.isArray(res) &&
          res[0] &&
          res[0].Status &&
          !isNaN(res[0].Status) &&
          res[0].Status !== '200'
        ) {
          if (res[0].Message.includes('Token')) {
            showToast('请重新登录')
            reLogin(1500)
            return Promise.reject(res[0])
          }
          
          console.log('failRequestUrl:', requestUrl)
          console.log('failRequestData:', requestData)
          console.log('failResponse:', responseData)
          
          if (res[0].Status === '201') return Promise.reject(res[0])
          
          if (res[0].Message.includes('实名验证')) {
            showDialog({
              title: '温馨提示',
              message: res[0].Message,
              confirmButtonText: '去设置',
              onConfirm: () => {
                Taro.navigateTo({
                  url: '/subpackages/pages/myKyc/index'
                })
              }
            })
          } else if (res[0].Message.includes('会员不能购买免费产品')) {
            showDialog({
              title: '温馨提示',
              message: '尊敬的安免会员！在安免1号平台购买任一商品成为店主，可领取免费产品'
            })
          } else if (res[0].Message) {
            showDialog({
              title: '温馨提示',
              message: res[0].Message
            })
          } else {
            showToast('数据请求失败')
          }
          return Promise.reject(res[0])
        }
      } catch (error) {
        showToast('程序语法错误')
        return Promise.reject(error)
      }
    }
    
    try {
      if (responseData.d && typeof responseData.d === 'string') {
        responseData.d = JSON.parse(responseData.d.replace(/\n/gi, '\\n').replace(/\r/gi, '\\n'))
      }
      console.log(responseData)
      return response
    } catch (error) {
      showToast('程序语法错误')
      return Promise.reject(error)
    }
  } catch (error) {
    showToast('数据请求失败')
    return Promise.reject(error)
  }
}

// 导出请求方法
export const post = (url: string, options?: { data?: any }) => {
  return request({
    url,
    data: options?.data,
    method: 'POST'
  })
}

export const get = (url: string, options?: { data?: any }) => {
  return request({
    url,
    data: options?.data,
    method: 'GET'
  })
}

export default {
  post,
  get,
  request
}
