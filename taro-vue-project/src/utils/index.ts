// 工具函数 - 从WePY项目迁移到Taro
import Taro from '@tarojs/taro'

/**
 * 限制充值金额只能输入小数点后2位
 * @param val 输入值
 * @returns 格式化后的数值
 */
export const limitCash = (val: string | number): string | number => {
  let num = val.toString() // 先转换成字符串类型
  if (num.indexOf('.') === 0) {
    // 第一位就是 .
    num = '0' + num
  }
  num = num.replace(/[^\d.]/g, '') // 清除"数字"和"."以外的字符
  num = num.replace(/\.{2,}/g, '.') // 只保留第一个. 清除多余的
  num = num.replace('.', '$#$').replace(/\./g, '').replace('$#$', '.')
  num = num.replace(/^(\-)*(\d+)\.(\d\d).*$/, '$1$2.$3') // 只能输入两个小数
  if (num.indexOf('.') < 0 && num !== '') {
    return parseFloat(num)
  }
  return num
}

/**
 * 检查位置权限状态
 * @returns Promise
 */
export const checkLocationAuth = async (): Promise<void> => {
  try {
    const setting = await Taro.getSetting()
    if (setting.authSetting['scope.userFuzzyLocation'] === false) {
      // 用户曾经拒绝过授权，显示重新授权对话框
      return new Promise((resolve, reject) => {
        Taro.showModal({
          title: '需要位置权限',
          content: '为了提供更好的服务，需要获取您的位置信息，是否前往设置打开？',
          confirmText: '前往设置',
          cancelText: '暂不开启',
          success: (res) => {
            if (res.confirm) {
              // 打开设置页面
              Taro.openSetting({
                success: (settingRes) => {
                  if (settingRes.authSetting['scope.userFuzzyLocation']) {
                    resolve()
                  } else {
                    reject(new Error('用户未授权位置信息'))
                  }
                },
                fail: reject
              })
            } else {
              reject(new Error('用户取消授权'))
            }
          },
          fail: reject
        })
      })
    }
    return Promise.resolve()
  } catch (error) {
    return Promise.reject(error)
  }
}

/**
 * 获取位置信息
 * @returns Promise<LocationInfo>
 */
export const getLocation = async (): Promise<{
  State: string
  City: string
  District: string
}> => {
  try {
    // 先检查权限
    await checkLocationAuth()

    // 获取位置信息
    const location = await new Promise<any>((resolve, reject) => {
      Taro.getFuzzyLocation({
        type: 'wgs84',
        success: resolve,
        fail: (error) => {
          if (error.errMsg.includes('auth deny')) {
            // 用户在系统弹窗中拒绝授权
            checkLocationAuth()
              .then(() => getLocation())
              .catch(reject)
          } else {
            reject(error)
          }
        }
      })
    })

    // 这里可以集成腾讯地图或其他地图服务进行逆地址解析
    // 暂时返回模拟数据
    return {
      State: '广东省',
      City: '深圳市',
      District: '南山区'
    }
  } catch (error) {
    console.error('获取位置失败：', error)
    Taro.showToast({
      title: (error as Error).message || '获取位置失败',
      icon: 'none'
    })
    throw error
  }
}

/**
 * 格式化时间
 * @param time 时间戳或时间字符串
 * @param format 格式化模板
 * @returns 格式化后的时间字符串
 */
export const formatTime = (time: number | string | Date, format = 'YYYY-MM-DD HH:mm:ss'): string => {
  const date = new Date(time)
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')
  const seconds = String(date.getSeconds()).padStart(2, '0')

  return format
    .replace('YYYY', String(year))
    .replace('MM', month)
    .replace('DD', day)
    .replace('HH', hours)
    .replace('mm', minutes)
    .replace('ss', seconds)
}

/**
 * 防抖函数
 * @param func 要防抖的函数
 * @param wait 等待时间
 * @returns 防抖后的函数
 */
export const debounce = <T extends (...args: any[]) => any>(
  func: T,
  wait: number
): ((...args: Parameters<T>) => void) => {
  let timeout: NodeJS.Timeout | null = null
  return (...args: Parameters<T>) => {
    if (timeout) clearTimeout(timeout)
    timeout = setTimeout(() => func(...args), wait)
  }
}

/**
 * 节流函数
 * @param func 要节流的函数
 * @param wait 等待时间
 * @returns 节流后的函数
 */
export const throttle = <T extends (...args: any[]) => any>(
  func: T,
  wait: number
): ((...args: Parameters<T>) => void) => {
  let timeout: NodeJS.Timeout | null = null
  return (...args: Parameters<T>) => {
    if (!timeout) {
      timeout = setTimeout(() => {
        timeout = null
        func(...args)
      }, wait)
    }
  }
}

/**
 * 深拷贝
 * @param obj 要拷贝的对象
 * @returns 拷贝后的对象
 */
export const deepClone = <T>(obj: T): T => {
  if (obj === null || typeof obj !== 'object') return obj
  if (obj instanceof Date) return new Date(obj.getTime()) as unknown as T
  if (obj instanceof Array) return obj.map(item => deepClone(item)) as unknown as T
  if (typeof obj === 'object') {
    const clonedObj = {} as T
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        clonedObj[key] = deepClone(obj[key])
      }
    }
    return clonedObj
  }
  return obj
}

/**
 * 生成唯一ID
 * @returns 唯一ID字符串
 */
export const generateId = (): string => {
  return Date.now().toString(36) + Math.random().toString(36).substr(2)
}

/**
 * 数组去重
 * @param arr 要去重的数组
 * @param key 去重的键名（对象数组时使用）
 * @returns 去重后的数组
 */
export const uniqueArray = <T>(arr: T[], key?: keyof T): T[] => {
  if (!key) {
    return [...new Set(arr)]
  }
  const seen = new Set()
  return arr.filter(item => {
    const value = item[key]
    if (seen.has(value)) {
      return false
    }
    seen.add(value)
    return true
  })
}

/**
 * 格式化文件大小
 * @param bytes 字节数
 * @returns 格式化后的文件大小
 */
export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

/**
 * 验证手机号
 * @param phone 手机号
 * @returns 是否有效
 */
export const validatePhone = (phone: string): boolean => {
  const phoneRegex = /^1[3-9]\d{9}$/
  return phoneRegex.test(phone)
}

/**
 * 验证邮箱
 * @param email 邮箱
 * @returns 是否有效
 */
export const validateEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

/**
 * 验证身份证号
 * @param idCard 身份证号
 * @returns 是否有效
 */
export const validateIdCard = (idCard: string): boolean => {
  const idCardRegex = /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/
  return idCardRegex.test(idCard)
}

/**
 * 获取URL参数
 * @param url URL字符串
 * @returns 参数对象
 */
export const getUrlParams = (url: string): Record<string, string> => {
  const params: Record<string, string> = {}
  const urlObj = new URL(url)
  urlObj.searchParams.forEach((value, key) => {
    params[key] = value
  })
  return params
}

/**
 * 存储数据到本地
 * @param key 键名
 * @param data 数据
 */
export const setStorage = (key: string, data: any): void => {
  try {
    Taro.setStorageSync(key, JSON.stringify(data))
  } catch (error) {
    console.error('存储数据失败:', error)
  }
}

/**
 * 从本地获取数据
 * @param key 键名
 * @returns 数据
 */
export const getStorage = <T = any>(key: string): T | null => {
  try {
    const data = Taro.getStorageSync(key)
    return data ? JSON.parse(data) : null
  } catch (error) {
    console.error('获取数据失败:', error)
    return null
  }
}

/**
 * 删除本地数据
 * @param key 键名
 */
export const removeStorage = (key: string): void => {
  try {
    Taro.removeStorageSync(key)
  } catch (error) {
    console.error('删除数据失败:', error)
  }
}
