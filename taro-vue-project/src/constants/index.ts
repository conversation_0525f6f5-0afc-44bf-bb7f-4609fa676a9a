// 常量定义 - 从WePY2的const.js迁移到Taro
// 微信小程序订阅消息模板ID
export const SUBSCRIBE_TEMPLATES = {
  // 订单已发货提醒
  ORDER_SHIPPED: '9zLAWunfMQvLS_OfifWC1BjxTb4djXs38AUeQxELXNc',
  // 待付款提醒
  PENDING_PAYMENT: 'hdVxZU5M1R-5rDbJEBiOrv1yYryGaj6bjQ0uMw0n_VQ',
  // 新成员加入通知
  NEW_MEMBER_JOIN: '8S4cckDPAXTjzhzc5A7RTB08IyYqA_KDQBe7bMzb5A0',
  // 商品到货提醒
  PRODUCT_ARRIVAL: 'HbQdqxskpQ32M01p-Q6MXYob1egodcH8sYurWRjgj1Q',
  // 活动开始提醒
  ACTIVITY_START: '4kxi582JKY1Et-RO3LiM9abE0W8A76HCb5lIqi51SmA'
}

// 错误消息映射
export const ERROR_MESSAGES = {
  20004: '您已关闭消息订阅开关，请到右上角"设置"中打开'
}

// 贸易分类
export const TRADE_CATEGORY = {
  NT: '完税',
  SH: '自营',
  TI: '保税'
}

// 订单状态
export const ORDER_STATUS = {
  PENDING_PAYMENT: 'pending_payment', // 待付款
  PENDING_SHIPMENT: 'pending_shipment', // 待发货
  SHIPPED: 'shipped', // 已发货
  COMPLETED: 'completed', // 已完成
  CANCELLED: 'cancelled', // 已取消
  REFUNDING: 'refunding', // 退款中
  REFUNDED: 'refunded' // 已退款
}

// 订单状态文本
export const ORDER_STATUS_TEXT = {
  [ORDER_STATUS.PENDING_PAYMENT]: '待付款',
  [ORDER_STATUS.PENDING_SHIPMENT]: '待发货',
  [ORDER_STATUS.SHIPPED]: '已发货',
  [ORDER_STATUS.COMPLETED]: '已完成',
  [ORDER_STATUS.CANCELLED]: '已取消',
  [ORDER_STATUS.REFUNDING]: '退款中',
  [ORDER_STATUS.REFUNDED]: '已退款'
}

// 支付方式
export const PAYMENT_METHODS = {
  WECHAT: 'wechat', // 微信支付
  ALIPAY: 'alipay', // 支付宝
  BALANCE: 'balance' // 余额支付
}

// 支付方式文本
export const PAYMENT_METHOD_TEXT = {
  [PAYMENT_METHODS.WECHAT]: '微信支付',
  [PAYMENT_METHODS.ALIPAY]: '支付宝',
  [PAYMENT_METHODS.BALANCE]: '余额支付'
}

// 商品类型
export const PRODUCT_TYPES = {
  NORMAL: 'normal', // 普通商品
  GROUP: 'group', // 团购商品
  LIMIT: 'limit', // 限时商品
  FREE: 'free' // 免费商品
}

// 用户类型
export const USER_TYPES = {
  NORMAL: 'normal', // 普通用户
  VIP: 'vip', // VIP用户
  AGENT: 'agent' // 代理商
}

// 钱包类型
export const WALLET_TYPES = {
  EARNING: 'earning', // 收益钱包
  SHOPPING: 'shopping', // 购物钱包
  FROZEN: 'frozen' // 冻结钱包
}

// 钱包类型文本
export const WALLET_TYPE_TEXT = {
  [WALLET_TYPES.EARNING]: '收益钱包',
  [WALLET_TYPES.SHOPPING]: '购物钱包',
  [WALLET_TYPES.FROZEN]: '冻结钱包'
}

// 收益类型
export const INCOME_TYPES = {
  SHARE: 'share', // 分享收益
  GROUP: 'group', // 团购收益
  PROMOTION: 'promotion', // 推广收益
  THANKFUL: 'thankful', // 感恩收益
  REDPACK: 'redpack' // 红包收益
}

// 收益类型文本
export const INCOME_TYPE_TEXT = {
  [INCOME_TYPES.SHARE]: '分享收益',
  [INCOME_TYPES.GROUP]: '团购收益',
  [INCOME_TYPES.PROMOTION]: '推广收益',
  [INCOME_TYPES.THANKFUL]: '感恩收益',
  [INCOME_TYPES.REDPACK]: '红包收益'
}

// 页面路径常量
export const PAGE_PATHS = {
  INDEX: '/pages/index/index',
  CLASSIFICATION: '/pages/classification/index',
  CART: '/pages/cart/index',
  MY: '/pages/my/index',
  LOGIN: '/subpackages/pages/login2/index',
  PRODUCT_DETAIL: '/pages/proDetail/index',
  ORDER_LIST: '/pages/order/index',
  ORDER_DETAIL: '/pages/orderDetail/index'
}

// 存储键名常量
export const STORAGE_KEYS = {
  TOKEN: 'Token',
  USER_INFO: 'userInfo',
  MEMBER_CODE: 'MemberCode',
  CURRENT_PAGE: 'currentPage',
  SEARCH_HISTORY: 'searchHistory',
  CART_COUNT: 'cartCount'
}

// 默认配置
export const DEFAULT_CONFIG = {
  PAGE_SIZE: 10, // 默认分页大小
  REQUEST_TIMEOUT: 10000, // 请求超时时间
  IMAGE_QUALITY: 80, // 图片质量
  MAX_UPLOAD_SIZE: 5 * 1024 * 1024 // 最大上传文件大小 5MB
}

// 正则表达式
export const REGEX = {
  PHONE: /^1[3-9]\d{9}$/, // 手机号
  EMAIL: /^[^\s@]+@[^\s@]+\.[^\s@]+$/, // 邮箱
  ID_CARD: /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/, // 身份证
  PASSWORD: /^(?=.*[a-zA-Z])(?=.*\d)[a-zA-Z\d]{6,20}$/ // 密码：6-20位字母数字组合
}

// 导出所有常量（兼容原来的导出方式）
export const 订单已发货提醒 = SUBSCRIBE_TEMPLATES.ORDER_SHIPPED
export const 待付款提醒 = SUBSCRIBE_TEMPLATES.PENDING_PAYMENT
export const 新成员加入通知 = SUBSCRIBE_TEMPLATES.NEW_MEMBER_JOIN
export const 商品到货提醒 = SUBSCRIBE_TEMPLATES.PRODUCT_ARRIVAL
export const 活动开始提醒 = SUBSCRIBE_TEMPLATES.ACTIVITY_START
export const errMsg = ERROR_MESSAGES
export const TradeCategory = TRADE_CATEGORY
